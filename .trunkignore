# Trunk ignore file - specifies files/directories to exclude from trunk check
# Documentation: https://docs.trunk.io/check/reference/trunk-ignore

# Build outputs
.next/
.turbo/
dist/
build/
out/
coverage/

# Node modules
node_modules/

# Dependencies
pnpm-lock.yaml
package-lock.json
yarn.lock

# TypeScript build info
tsconfig.tsbuildinfo
*.d.ts

# Generated files
*.generated.*
packages/types/src/database.ts

# Environment files
.env*

# Logs
logs/
*.log

# IDE and editor files
.vscode/
.idea/
.claude/

# Test outputs
test-results/
playwright-report/
performance-report.json
load-test-report.json

# Sentry and instrumentation
sentry.*.config.*
instrumentation*.ts

# External projects (not part of main codebase)
archon/
serena/

# Temporary files
temp-*/
tmp.*/
.tmp/

# Archives and backups
archives/
backups/
deprecated/
legacy/

# Infrastructure scripts (managed separately)
infrastructure/scripts/

# Migration files (auto-generated)
supabase/migrations/

# Config files that are managed by their respective tools
dprint.json
.oxlintrc.json
biome.json
tailwind.config.*
next.config.*
vitest.config.*
playwright.config.*
turbo.json
trigger.config.*

# Documentation with specific formatting requirements
README.md
CHANGELOG.md
CLAUDE.md
AGENTS.md