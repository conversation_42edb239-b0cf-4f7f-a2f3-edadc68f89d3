import { vi } from "vitest";

// Mock node fetch modules to prevent DNS resolution
vi.mock("node:fetch", () => ({
  default: vi.fn(),
}));

vi.mock("undici", () => ({
  fetch: vi.fn(),
  request: vi.fn(),
}));

// Mock console for cleaner test output
global.console = {
  ...console,
  log: vi.fn(),
  warn: vi.fn(),
  error: vi.fn(),
};

// Mock environment variables
process.env = {
  ...process.env,
  NODE_ENV: "test",
  SUPABASE_URL: "http://mock-supabase-server",
  SUPABASE_ANON_KEY: "mock-anon-key",
  DATABASE_URL: "***********************************/mock_test",
  JWT_SECRET: "mock-secret",
  TEST_API_BASE_URL: "http://mock-api-server",
  TEST_SUPABASE_URL: "http://mock-supabase-server",
};

// Mock crypto
Object.defineProperty(global, "crypto", {
  value: {
    randomUUID: () => "test-uuid",
    getRandomValues: (arr: Uint8Array) => arr,
  },
});

// Create a fetch mock
const mockFetch = vi.fn(
  async (input: RequestInfo | URL, init?: RequestInit) => {
    const url = typeof input === "string" ? input : input.toString();
    console.log("🔍 Mock fetch called with URL:", url);

    // Handle mock server URLs specifically
    if (url.includes("mock-api-server") || url.includes("mock-supabase-server")) {
      console.log("✅ Mock server URL detected:", url);
      // AI Services API endpoints
      if (url.includes("/api/ai/universal-chat/session")) {
        console.log("🎯 Session endpoint matched, returning mock data");
        return Promise.resolve({
          ok: true,
          status: 200,
          json: () =>
            Promise.resolve({
              success: true,
              session_id: "mock-session-123",
              user_id: "mock-user-456",
              created_at: new Date().toISOString(),
              status: "active",
              compliance_status: {
                lgpd_compliant: true,
                anvisa_compliant: true,
                cfm_compliant: true,
              },
            }),
          text: () => Promise.resolve(""),
          headers: new Headers({ "content-type": "application/json" }),
        } as Response);
      }

      if (url.includes("/api/ai/universal-chat/message")) {
        return Promise.resolve({
          ok: true,
          status: 200,
          json: () =>
            Promise.resolve({
              success: true,
              message_id: "mock-message-789",
              response: "Mock AI response for testing healthcare query",
              timestamp: new Date().toISOString(),
              tokens_used: 50,
              emergency_detected: url.includes("emergency") || url.includes("192"),
              emergency_response: {
                priority: "critical",
                instructions: "Call 192 immediately for emergency assistance",
              },
              safety_assessment: {
                emergency_detected: false,
                suicide_risk: 0.1,
                violence_risk: 0.05,
              },
            }),
          text: () => Promise.resolve(""),
          headers: new Headers({ "content-type": "application/json" }),
        } as Response);
      }

      if (url.includes("/api/ai/compliance")) {
        return Promise.resolve({
          ok: true,
          status: 200,
          json: () =>
            Promise.resolve({
              success: true,
              compliance_status: {
                lgpd_compliant: !url.includes("non-compliant"),
                anvisa_compliant: true,
                cfm_compliant: true,
              },
              violations: url.includes("non-compliant")
                ? [{
                  type: "lgpd",
                  severity: "high",
                  description: "Mock LGPD violation",
                }]
                : [],
              device_validation: {
                registration_valid: true,
                certification_status: "active",
              },
              telemedicine_validation: {
                crm_valid: true,
                license_status: "active",
              },
              report: {
                summary: "Mock compliance report summary",
                lgpd_compliance_rate: 0.98,
                anvisa_compliance_rate: 1.0,
                cfm_compliance_rate: 0.99,
              },
            }),
          text: () => Promise.resolve(""),
          headers: new Headers({ "content-type": "application/json" }),
        } as Response);
      }

      if (url.includes("/api/ai/conversation")) {
        return Promise.resolve({
          ok: true,
          status: 200,
          json: () =>
            Promise.resolve({
              success: true,
              conversation_id: "mock-conv-123",
              messages: [],
              status: "active",
              sentiment_analysis: {
                overall_sentiment: "neutral",
                confidence: 0.85,
                emotions: ["calm", "concerned"],
              },
              topic_analysis: {
                primary_topics: ["healthcare", "symptoms"],
                medical_keywords: ["headache", "fever", "consultation"],
              },
              safety_assessment: {
                emergency_detected: false,
                suicide_risk: 0.1,
                violence_risk: 0.05,
              },
              summary: {
                main_topics: ["Patient consultation", "Symptom assessment"],
                patient_concerns: ["Headache symptoms", "Treatment options"],
                recommendations: ["Schedule follow-up", "Monitor symptoms"],
              },
              compliance_status: {
                lgpd_violations: 0,
                anvisa_violations: 0,
                cfm_violations: 0,
              },
            }),
          text: () => Promise.resolve(""),
          headers: new Headers({ "content-type": "application/json" }),
        } as Response);
      }

      // Supabase mock endpoints
      if (url.includes("supabase") || url.includes("mock-supabase-server")) {
        return Promise.resolve({
          ok: true,
          status: 200,
          json: () =>
            Promise.resolve({
              data: [],
              error: null,
              count: 0,
            }),
          text: () => Promise.resolve(""),
          headers: new Headers({ "content-type": "application/json" }),
        } as Response);
      }
    }

    // For all other API routes, return generic mock response
    return Promise.resolve({
      ok: true,
      status: 200,
      json: () => Promise.resolve({ success: true, data: {} }),
      text: () => Promise.resolve(""),
      headers: new Headers({
        "content-type": "application/json",
      }),
    } as Response);
  },
);

// Add mock methods that tests expect
mockFetch.mockClear = vi.fn();
mockFetch.mockResolvedValue = vi.fn();
mockFetch.mockRejectedValue = vi.fn();

// Mock fetch globally and prevent DNS resolution
Object.defineProperty(global, "fetch", {
  value: mockFetch,
  writable: true,
  configurable: true,
});

// Also set it on globalThis for better compatibility
Object.defineProperty(globalThis, "fetch", {
  value: mockFetch,
  writable: true,
  configurable: true,
});

// Mock the fetch function at module level
if (typeof window !== "undefined") {
  window.fetch = mockFetch;
}

// Mock localStorage
if (typeof window !== "undefined") {
  Object.defineProperty(window, "localStorage", {
    value: {
      getItem: vi.fn(() => null),
      setItem: vi.fn(() => null),
      removeItem: vi.fn(() => null),
      clear: vi.fn(() => null),
    },
    writable: true,
  });

  // Mock sessionStorage
  Object.defineProperty(window, "sessionStorage", {
    value: {
      getItem: vi.fn(() => null),
      setItem: vi.fn(() => null),
      removeItem: vi.fn(() => null),
      clear: vi.fn(() => null),
    },
    writable: true,
  });

  // Mock window.location
  Object.defineProperty(window, "location", {
    value: {
      href: "http://localhost:3000",
      origin: "http://localhost:3000",
      pathname: "/",
      search: "",
      hash: "",
    },
    writable: true,
  });
}
