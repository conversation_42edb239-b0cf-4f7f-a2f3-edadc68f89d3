{"name": "neonpro", "private": true, "scripts": {"preinstall": "echo 'Using Bun package manager for optimal performance'", "build": "turbo build", "dev": "turbo dev", "dev:api": "turbo dev --filter=api", "dev:web": "turbo dev --filter=web", "dev:full": "turbo dev", "start": "turbo start", "clean": "turbo clean", "test:unit": "vitest run --coverage", "test:watch": "vitest watch", "test:ui": "vitest --ui", "test:e2e": "playwright test", "test:e2e:headed": "playwright test --headed", "test:lint": "bun run lint", "test:type": "bun run type-check", "test:all": "bun run test:unit && bun run test:e2e && bun run test:lint && bun run test:type", "quality:check": "bun run test:all", "format": "dprint fmt", "format:check": "dprint check", "lint": "npx oxlint apps packages --max-warnings=10", "lint:fix": "bunx oxlint apps packages --fix && dprint fmt", "type-check": "tsc --noEmit", "trunk": "trunk check --all", "ci:check": "bun run format:check && bun run lint && bun run type-check", "ci:fix": "bun run format && bun run lint:fix && bun run type-check", "test": "turbo test", "test:unit:watch": "vitest --reporter=verbose", "test:unit:ui": "vitest --ui", "test:unit:coverage": "vitest run --coverage", "test:integration": "vitest run --config vitest.config.integration.ts", "test:integration:watch": "vitest --config vitest.config.integration.ts", "test:integration:ui": "vitest --ui --config vitest.config.integration.ts", "test:integration:coverage": "vitest run --coverage --config vitest.config.integration.ts", "test:e2e:ui": "playwright test --ui", "test:e2e:debug": "playwright test --debug", "test:e2e:install": "playwright install --with-deps", "test:safe": "bun run ci:check && bun run test:unit:safe && echo \"✅ Safe test suite completed successfully\"", "test:unit:safe": "vitest run apps/web/tests/core apps/web/tests/components/ui apps/web/tests/hooks/simple-auth.test.tsx --no-coverage", "test:healthcare": "bun run test:integration -- --testNamePattern=\"(Patient|LGPD|Emergency)\"", "build:production": "NODE_ENV=production turbo build --filter=web && bun run validate:production", "validate:production": "bun run type-check && bun run lint && bun run test:unit:safe", "deploy:vercel": "bun run build:production && vercel --prod --yes", "deploy:staging": "bun run build:production && vercel --yes", "health:check": "curl -f http://localhost:3000/api/health || echo 'Health check failed'", "monitor:production": "bun run health:check && echo '✅ Production monitoring active'", "test:ai": "vitest run --testNamePattern=\"AI|LLM|GPT\" --reporter=verbose", "benchmark": "vitest bench --reporter=verbose", "security:audit": "echo 'Security audit - checking for vulnerabilities...' && bun audit || echo 'Audit completed with warnings'", "security:check": "bun run security:audit && bun run ci:check", "deps:update": "bun update --interactive --latest", "deps:check": "bun outdated", "oxlint:fix": "npx oxlint apps packages --fix", "prepare": "husky"}, "devDependencies": {"@oxlint/win32-x64": "^1.13.0", "@playwright/test": "^1.54.2", "@rollup/rollup-win32-x64-msvc": "^4.49.0", "@testing-library/dom": "^10.4.1", "@testing-library/jest-dom": "^6.8.0", "@testing-library/react": "^16.3.0", "@testing-library/user-event": "^14.6.1", "@turbo/gen": "^1.13.4", "@types/node": "^22.10.2", "@types/react": "^18.3.18", "@types/react-dom": "^18.3.5", "@vitest/coverage-v8": "^3.2.4", "@vitest/ui": "^3.2.4", "dotenv": "^17.2.1", "dprint": "^0.50.1", "happy-dom": "^18.0.1", "husky": "^9.1.7", "jsdom": "^26.1.0", "msw": "^2.10.5", "node-mocks-http": "^1.17.2", "oxc": "^1.0.1", "oxlint": "^1.13.0", "playwright": "^1.55.0", "prettier": "^3.6.2", "sonner": "^2.0.7", "turbo": "^2.5.6", "turbo-windows-64": "^2.5.6", "typescript": "^5.0.0", "vitest": "^3.2.4"}, "packageManager": "bun@1.2.21", "engines": {"node": ">=18"}, "dependencies": {"@radix-ui/react-avatar": "^1.0.4", "@radix-ui/react-popover": "^1.0.7", "@radix-ui/react-progress": "^1.1.7", "@radix-ui/react-scroll-area": "^1.2.10", "@radix-ui/react-select": "^2.0.0", "@radix-ui/react-slider": "^1.3.6", "@radix-ui/react-switch": "^1.2.6", "@radix-ui/react-tabs": "^1.0.4", "@sentry/nextjs": "^10.5.0", "@tanstack/react-query": "^5.62.2", "@tanstack/react-query-devtools": "^5.62.2", "@vitejs/plugin-react": "^5.0.0", "jspdf": "^3.0.1", "lru-cache": "^11.0.2", "lucide-react": "^0.541.0", "next": "^15.1.0", "optional": "^0.1.4", "react": "^19.1.1", "react-dom": "^19.1.1", "recharts": "^3.1.2", "tailwindcss": "^3.4.15", "xlsx": "0.18.5"}, "pnpm": {"overrides": {"react": "^19.1.1", "react-dom": "^19.1.1", "@types/react": "^19.0.0", "@types/react-dom": "^19.0.0", "glob": "^10.3.10", "rimraf": "^5.0.5", "uuid": "^10.0.0", "jose": "^5.6.3", "inflight": "^1.0.6", "source-map": "^0.8.0-beta.0", "xlsx": ">=0.19.3", "marked": ">=4.0.10", "hawk": ">=9.0.1"}, "peerDependencyRules": {"ignoreMissing": ["@algolia/client-search"], "allowAny": ["*"]}}}