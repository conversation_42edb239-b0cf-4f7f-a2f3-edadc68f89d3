{"name": "neonpro", "private": true, "scripts": {"preinstall": "npx only-allow pnpm", "build": "turbo build", "dev": "turbo dev", "dev:api": "turbo dev --filter=api", "dev:web": "turbo dev --filter=web", "dev:full": "turbo dev", "start": "turbo start", "clean": "turbo clean", "test:unit": "vitest run --coverage", "test:watch": "vitest watch", "test:ui": "vitest --ui", "test:e2e": "playwright test", "test:e2e:headed": "playwright test --headed", "test:lint": "pnpm lint", "test:type": "pnpm type-check", "test:all": "pnpm run test:unit && pnpm run test:e2e && pnpm run test:lint && pnpm run test:type", "quality:check": "pnpm run test:all", "format": "dprint fmt", "format:check": "dprint check", "lint": "npx @oxlint/win32-x64 oxlint apps packages --max-warnings=10", "lint:fix": "npx @oxlint/win32-x64 oxlint apps packages --fix && dprint fmt", "type-check": "tsc --noEmit", "trunk": "trunk check --all", "ci:check": "pnpm format:check && pnpm lint && pnpm type-check", "ci:fix": "pnpm format && pnpm lint:fix && pnpm type-check", "test": "turbo test", "test:unit:watch": "vitest --reporter=verbose", "test:unit:ui": "vitest --ui", "test:unit:coverage": "vitest run --coverage", "test:integration": "vitest run --config vitest.config.integration.ts", "test:integration:watch": "vitest --config vitest.config.integration.ts", "test:integration:ui": "vitest --ui --config vitest.config.integration.ts", "test:integration:coverage": "vitest run --coverage --config vitest.config.integration.ts", "test:e2e:ui": "playwright test --ui", "test:e2e:debug": "playwright test --debug", "test:e2e:install": "playwright install --with-deps", "test:safe": "pnpm ci:check && pnpm test:unit:safe && echo \"✅ Safe test suite completed successfully\"", "test:unit:safe": "vitest run apps/web/tests/core apps/web/tests/components/ui apps/web/tests/hooks/simple-auth.test.tsx --no-coverage", "test:healthcare": "pnpm test:integration -- --testNamePattern=\"(Patient|LGPD|Emergency)\"", "build:production": "NODE_ENV=production turbo build --filter=web && pnpm validate:production", "validate:production": "pnpm type-check && pnpm lint && pnpm test:unit:safe", "deploy:vercel": "pnpm build:production && vercel --prod --yes", "deploy:staging": "pnpm build:production && vercel --yes", "health:check": "curl -f http://localhost:3000/api/health || echo 'Health check failed'", "monitor:production": "pnpm health:check && echo '✅ Production monitoring active'", "prepare": "husky"}, "devDependencies": {"@oxlint/win32-x64": "^1.13.0", "@playwright/test": "^1.54.2", "@rollup/rollup-win32-x64-msvc": "^4.49.0", "@testing-library/dom": "^10.4.1", "@testing-library/jest-dom": "^6.8.0", "@testing-library/react": "^16.3.0", "@testing-library/user-event": "^14.6.1", "@turbo/gen": "^1.13.4", "@types/node": "^22.10.2", "@types/react": "^18.3.18", "@types/react-dom": "^18.3.5", "@vitest/ui": "^3.2.4", "dotenv": "^17.2.1", "dprint": "^0.50.1", "happy-dom": "^18.0.1", "husky": "^9.1.7", "jsdom": "^26.1.0", "msw": "^2.10.5", "node-mocks-http": "^1.17.2", "oxc": "^1.0.1", "oxlint": "^1.13.0", "playwright": "^1.55.0", "prettier": "^3.6.2", "sonner": "^2.0.7", "turbo": "^2.5.6", "turbo-windows-64": "^2.5.6", "typescript": "^5.0.0", "vitest": "^3.2.4"}, "packageManager": "pnpm@8.15.6", "engines": {"node": ">=18"}, "dependencies": {"@radix-ui/react-avatar": "^1.0.4", "@radix-ui/react-popover": "^1.0.7", "@radix-ui/react-progress": "^1.1.7", "@radix-ui/react-scroll-area": "^1.2.10", "@radix-ui/react-select": "^2.0.0", "@radix-ui/react-slider": "^1.3.6", "@radix-ui/react-switch": "^1.2.6", "@radix-ui/react-tabs": "^1.0.4", "@sentry/nextjs": "^10.5.0", "@tanstack/react-query": "^5.62.2", "@tanstack/react-query-devtools": "^5.62.2", "@vitejs/plugin-react": "^5.0.0", "jspdf": "^3.0.1", "lru-cache": "^11.0.2", "lucide-react": "^0.541.0", "next": "^15.1.0", "optional": "^0.1.4", "react": "^19.1.1", "react-dom": "^19.1.1", "recharts": "^3.1.2", "tailwindcss": "^3.4.15", "xlsx": "^0.18.5"}, "pnpm": {"overrides": {"react": "^19.1.1", "react-dom": "^19.1.1", "@types/react": "^19.0.0", "@types/react-dom": "^19.0.0", "glob": "^10.3.10", "rimraf": "^5.0.5", "uuid": "^10.0.0", "jose": "^5.6.3", "inflight": "^1.0.6", "source-map": "^0.8.0-beta.0"}, "peerDependencyRules": {"ignoreMissing": ["@algolia/client-search"], "allowAny": ["*"]}}}