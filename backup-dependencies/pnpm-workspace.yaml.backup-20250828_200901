packages:
  - apps/*
  - apps/docs
  - packages/*
  - packages/typescript-config
  - tools/*

catalog:
  "@radix-ui/react-accordion": ^1.2.1
  "@radix-ui/react-avatar": ^1.1.1
  "@radix-ui/react-dialog": ^1.1.2
  "@radix-ui/react-dropdown-menu": ^2.1.2
  "@radix-ui/react-label": ^2.1.0
  "@radix-ui/react-select": ^2.1.2
  "@radix-ui/react-tabs": ^1.1.1
  "@prisma/client": ^5.22.0
  "@supabase/supabase-js": ^2.39.0
  "@types/node": ^22.10.2
  "@types/react": ^18.3.18
  "@types/react-dom": ^18.3.5
  class-variance-authority: ^0.7.0
  clsx: ^2.1.1
  date-fns: ^4.1.0
  lucide-react: ^0.468.0
  next: ^15.1.0
  prisma: ^5.22.0
  react: ^18.3.1
  react-dom: ^18.3.1
  # Performance dependencies
  lru-cache: ^11.0.2
  ioredis: ^5.4.1
  web-vitals: ^4.2.4
  supabase: ^1.127.4
  tailwind-merge: ^2.5.4
  tailwindcss: ^3.4.15
  tailwindcss-animate: ^1.0.7
  turbo: ^2.5.5
  tsx: ^4.6.0
  typescript: ^5.7.2
  zod: ^3.23.8

onlyBuiltDependencies:
  - "@prisma/client"
  - "@prisma/engines"
  - "@sentry/cli"
  - "@vercel/speed-insights"
  - cypress
