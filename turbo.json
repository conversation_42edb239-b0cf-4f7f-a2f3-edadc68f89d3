{"$schema": "https://turbo.build/schema.json", "globalDependencies": ["**/.env.*local", "**/package.json", "**/tsconfig.json", "dprint.json", "pnpm-workspace.yaml"], "globalEnv": ["NODE_ENV", "VERCEL_ENV", "CI", "TURBO_TOKEN", "TURBO_TEAM", "SUPABASE_URL", "SUPABASE_ANON_KEY", "SUPABASE_SERVICE_ROLE_KEY", "SUPABASE_PROJECT_ID", "DATABASE_URL", "DIRECT_URL", "LGPD_COMPLIANCE_KEY", "ANVISA_API_KEY", "CFM_VALIDATION_KEY", "NEXT_PUBLIC_SUPABASE_URL", "NEXT_PUBLIC_SUPABASE_ANON_KEY", "OPENAI_API_KEY", "ANTHROPIC_API_KEY"], "remoteCache": {"signature": true}, "globalPassThroughEnv": ["CI", "NODE_ENV", "VERCEL_ENV"], "tasks": {"build": {"dependsOn": ["^build"], "inputs": ["src/**/*.{ts,tsx,js,jsx}", "styles/**/*.{css,scss}", "public/**/*", "package.json", "tsconfig.json", "next.config.*", "tailwind.config.*", "!*/**/__tests__/**", "!*/**/*.test.*", "!*/**/*.spec.*"], "outputs": [".next/**", "!.next/cache/**", "dist/**", "build/**"], "env": ["NODE_ENV", "NEXT_PUBLIC_SUPABASE_URL", "NEXT_PUBLIC_SUPABASE_ANON_KEY", "SUPABASE_SERVICE_ROLE_KEY"], "outputLogs": "hash-only"}, "@neonpro/types#build": {"inputs": ["src/**/*.{ts,js}", "package.json", "tsconfig.json"], "outputs": ["dist/**"]}, "@neonpro/database#build": {"dependsOn": ["@neonpro/types#build"], "inputs": ["src/**/*.{ts,js}", "prisma/**/*.prisma", "supabase/**/*.sql", "types/**/*.ts", "package.json", "tsconfig.json"], "outputs": ["dist/**", "prisma/generated/**"]}, "@neonpro/cache#build": {"dependsOn": ["@neonpro/types#build", "@neonpro/database#build"], "inputs": ["src/**/*.{ts,js}", "package.json", "tsconfig.json"], "outputs": ["dist/**"]}, "@neonpro/audit-trail#build": {"dependsOn": ["@neonpro/types#build", "@neonpro/database#build"], "inputs": ["src/**/*.{ts,js}", "package.json", "tsconfig.json"], "outputs": ["dist/**"]}, "@neonpro/compliance#build": {"dependsOn": ["@neonpro/types#build", "@neonpro/audit-trail#build"], "inputs": ["src/**/*.{ts,js}", "package.json", "tsconfig.json"], "outputs": ["dist/**"]}, "@neonpro/constitutional-layer#build": {"dependsOn": ["@neonpro/types#build", "@neonpro/compliance#build", "@neonpro/audit-trail#build"], "inputs": ["src/**/*.{ts,js}", "package.json", "tsconfig.json"], "outputs": ["dist/**"]}, "@neonpro/auth#build": {"dependsOn": ["@neonpro/types#build", "@neonpro/database#build"], "inputs": ["src/**/*.{ts,js}", "package.json", "tsconfig.json"], "outputs": ["dist/**"]}, "@neonpro/shared#build": {"dependsOn": ["@neonpro/types#build", "@neonpro/database#build", "@neonpro/auth#build"], "inputs": ["src/**/*.{ts,js}", "package.json", "tsconfig.json"], "outputs": ["dist/**"]}, "@neonpro/monitoring#build": {"dependsOn": ["@neonpro/types#build", "@neonpro/database#build", "@neonpro/shared#build"], "inputs": ["src/**/*.{ts,js}", "package.json", "tsconfig.json"], "outputs": ["dist/**"]}, "@neonpro/devops#build": {"dependsOn": ["@neonpro/types#build", "@neonpro/database#build", "@neonpro/monitoring#build"], "inputs": ["src/**/*.{ts,js}", "package.json", "tsconfig.json"], "outputs": ["dist/**"]}, "@neonpro/ui#build": {"dependsOn": ["@neonpro/types#build", "@neonpro/shared#build"], "inputs": ["src/**/*.{ts,tsx,js,jsx}", "styles/**/*.{css,scss}", "package.json", "tsconfig.json"], "outputs": ["dist/**"]}, "@neonpro/domain#build": {"dependsOn": ["@neonpro/types#build", "@neonpro/database#build", "@neonpro/shared#build"], "inputs": ["src/**/*.{ts,js}", "package.json", "tsconfig.json"], "outputs": ["dist/**"]}, "@neonpro/ai#build": {"dependsOn": ["@neonpro/types#build", "@neonpro/cache#build", "@neonpro/constitutional-layer#build"], "inputs": ["src/**/*.{ts,js}", "package.json", "tsconfig.json"], "outputs": ["dist/**"]}, "@neonpro/core-services#build": {"dependsOn": ["@neonpro/types#build", "@neonpro/database#build", "@neonpro/shared#build"], "inputs": ["src/**/*.{ts,js}", "package.json", "tsconfig.json"], "outputs": ["dist/**"]}, "@neonpro/config#build": {"dependsOn": ["@neonpro/types#build"], "inputs": ["src/**/*.{ts,js}", "package.json", "tsconfig.json"], "outputs": ["dist/**"]}, "@neonpro/utils#build": {"dependsOn": ["@neonpro/types#build"], "inputs": ["src/**/*.{ts,js}", "package.json", "tsconfig.json"], "outputs": ["dist/**"]}, "@neonpro/security#build": {"dependsOn": ["@neonpro/types#build", "@neonpro/database#build", "@neonpro/audit-trail#build"], "inputs": ["src/**/*.{ts,js}", "package.json", "tsconfig.json"], "outputs": ["dist/**"]}, "@neonpro/integrations#build": {"dependsOn": ["@neonpro/types#build", "@neonpro/database#build", "@neonpro/auth#build"], "inputs": ["src/**/*.{ts,js}", "package.json", "tsconfig.json"], "outputs": ["dist/**"]}, "@neonpro/brazilian-healthcare-ui#build": {"dependsOn": ["@neonpro/types#build", "@neonpro/ui#build", "@neonpro/shared#build"], "inputs": ["src/**/*.{ts,tsx,js,jsx}", "styles/**/*.{css,scss}", "package.json", "tsconfig.json"], "outputs": ["dist/**"]}, "@neonpro/health-dashboard#build": {"dependsOn": ["@neonpro/types#build", "@neonpro/ui#build", "@neonpro/brazilian-healthcare-ui#build", "@neonpro/monitoring#build"], "inputs": ["src/**/*.{ts,tsx,js,jsx}", "styles/**/*.{css,scss}", "package.json", "tsconfig.json"], "outputs": ["dist/**"]}, "@neonpro/enterprise#build": {"dependsOn": ["@neonpro/types#build", "@neonpro/database#build", "@neonpro/auth#build", "@neonpro/security#build", "@neonpro/compliance#build"], "inputs": ["src/**/*.{ts,js}", "package.json", "tsconfig.json"], "outputs": ["dist/**"]}, "@neonpro/docs#build": {"dependsOn": ["@neonpro/types#build"], "inputs": ["src/**/*.{ts,js}", "package.json", "tsconfig.json"], "outputs": ["dist/**"]}, "lint": {"inputs": ["**/*.{ts,tsx,js,jsx}", "dprint.json"], "outputs": [], "outputLogs": "new-only"}, "lint:fix": {"inputs": ["**/*.{ts,tsx,js,jsx}", "dprint.json"], "outputs": [], "outputLogs": "new-only"}, "type-check": {"dependsOn": ["^build"], "inputs": ["**/*.{ts,tsx}", "**/tsconfig.json"], "outputs": ["**/*.tsbuildinfo"], "outputLogs": "errors-only"}, "format": {"inputs": ["**/*.{ts,tsx,js,jsx,json,md,toml,yaml,yml,css,scss,sass,less}", "dprint.json"], "outputs": [], "outputLogs": "new-only"}, "format:check": {"inputs": ["**/*.{ts,tsx,js,jsx,json,md,toml,yaml,yml,css,scss,sass,less}", "dprint.json"], "outputs": [], "outputLogs": "new-only"}, "test": {"dependsOn": ["build"], "inputs": ["**/*.test.*", "**/*.spec.*", "**/vitest.config.*"], "outputs": ["coverage/**", "tools/testing/reports/**"], "outputLogs": "full"}, "test:unit": {"dependsOn": ["build"], "inputs": ["**/*.test.*", "**/*.spec.*", "vitest.config.*"], "outputs": ["coverage/**"]}, "test:e2e": {"dependsOn": ["build"], "inputs": ["**/*.e2e.*", "**/e2e/**", "playwright.config.*"], "outputs": ["playwright-report/**", "test-results/**"]}, "test:e2e:install": {"cache": false, "outputs": []}, "test:healthcare": {"dependsOn": ["@neonpro/compliance#build", "@neonpro/audit-trail#build", "@neonpro/constitutional-layer#build"], "inputs": ["**/healthcare*.test.*", "**/compliance*.spec.*", "**/anvisa*.test.*", "**/lgpd*.spec.*"], "outputs": ["coverage/healthcare/**"]}, "compliance:validate": {"dependsOn": ["@neonpro/compliance#build", "@neonpro/audit-trail#build", "@neonpro/constitutional-layer#build"], "inputs": ["packages/compliance/src/**/*.{ts,js}", "packages/audit-trail/src/**/*.{ts,js}", "packages/constitutional-layer/src/**/*.{ts,js}"], "outputs": ["compliance-reports/**"]}, "compliance:lgpd": {"dependsOn": ["@neonpro/compliance#build"], "inputs": ["packages/compliance/src/lgpd/**/*.{ts,js}"], "outputs": ["compliance-reports/lgpd/**"]}, "compliance:anvisa": {"dependsOn": ["@neonpro/compliance#build"], "inputs": ["packages/compliance/src/anvisa/**/*.{ts,js}"], "outputs": ["compliance-reports/anvisa/**"]}, "ci:check": {"dependsOn": ["format:check", "lint", "type-check", "test:healthcare"], "inputs": ["**/*.{ts,tsx,js,jsx}", "dprint.json"], "outputs": [], "outputLogs": "full"}, "ci-check": {"dependsOn": ["lint", "type-check", "test:healthcare"], "inputs": ["**/*.{ts,tsx,js,jsx}", "dprint.json"], "outputs": [], "outputLogs": "full"}, "deploy:blue-green": {"dependsOn": ["@neonpro/devops#build", "build", "test:healthcare", "compliance:validate"], "cache": false, "inputs": ["packages/devops/src/deployment/**/*.{ts,js}", "apps/**/.next/**"], "outputs": []}, "deploy:staging": {"dependsOn": ["deploy:blue-green"], "cache": false, "outputs": []}, "deploy:production": {"dependsOn": ["deploy:blue-green", "test:healthcare", "compliance:validate"], "cache": false, "outputs": []}, "health:check": {"dependsOn": ["@neonpro/devops#build", "@neonpro/monitoring#build"], "cache": false, "inputs": ["packages/devops/src/deployment/**/*.{ts,js}", "packages/monitoring/src/**/*.{ts,js}"], "outputs": []}, "monitoring:start": {"dependsOn": ["@neonpro/monitoring#build"], "cache": false, "persistent": true}, "supabase:build": {"dependsOn": ["@neonpro/database#build"], "inputs": ["supabase/**/*.sql", "packages/database/prisma/**/*.prisma", "infrastructure/database/**/*.sql"], "outputs": ["supabase/generated/**"], "env": ["SUPABASE_URL", "SUPABASE_SERVICE_ROLE_KEY", "DATABASE_URL"]}, "supabase:generate": {"dependsOn": ["supabase:build"], "cache": true, "inputs": ["packages/database/prisma/schema.prisma"], "outputs": ["packages/database/prisma/generated/**"]}, "dev": {"cache": false, "persistent": true, "env": ["NODE_ENV", "NEXT_PUBLIC_SUPABASE_URL", "NEXT_PUBLIC_SUPABASE_ANON_KEY", "SUPABASE_SERVICE_ROLE_KEY"]}, "clean": {"cache": false, "outputs": []}}}