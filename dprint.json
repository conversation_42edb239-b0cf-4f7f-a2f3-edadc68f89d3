{"$schema": "https://dprint.dev/schemas/v0.json", "lineWidth": 100, "indentWidth": 2, "useTabs": false, "includes": ["**/*.{ts,tsx,js,jsx,mjs,cjs,mts,cts,json,jsonc,md,toml,yaml,yml,css,scss,sass,less,Dockerfile}"], "excludes": ["**/.git/**", "**/.trunk/**", "**/.turbo/**", "**/.next/**", "**/node_modules/**", "**/dist/**", "**/build/**", "**/coverage/**", "**/playwright-report/**", "**/.vercel/**", "**/.claude/**", "**/.github/**", "**/.vscode/**", "**/.idea/**", "**/.trae/**", "**/.ruler/**", "**/tools/testing/reports/assets/**", "**/tools/e2e/reports/**", "**/tools/reports/**", "**/test-results/**", "**/performance-report.json", "**/load-test-report.json", "archon/**", "serena/**", "**/temp-*/**", "**/tmp.*/**", "**/.tmp/**", "**/archives/**", "**/backups/**", "**/deprecated/**", "**/legacy/**", "**/pnpm-lock.yaml", "**/package-lock.json", "**/yarn.lock", "**/tsconfig.tsbuildinfo", "**/*.d.ts", "**/packages/types/src/database.ts", "**/*.generated.*", "**/supabase/migrations/**", "**/supabase/types/**", "**/logs/**", "**/.env*", "**/sentry.*.config.*", "**/instrumentation*.ts", "**/*.chatmode.md"], "typescript": {"quoteStyle": "preferDouble", "semiColons": "always", "trailingCommas": "onlyMultiLine", "operatorPosition": "nextLine", "module.sortImportDeclarations": "caseInsensitive", "module.sortExportDeclarations": "caseInsensitive", "useBraces": "whenNotSingleLine", "bracePosition": "sameLineUnlessHanging", "nextControlFlowPosition": "sameLine", "singleBodyPosition": "maintain", "jsx.quoteStyle": "preferDouble", "jsx.multiLineParens": "prefer", "commentLine.forceSpaceAfterSlashes": true}, "json": {"indentWidth": 2, "trailingCommas": "never"}, "markdown": {"textWrap": "maintain"}, "yaml": {"indentWidth": 2}, "plugins": ["https://plugins.dprint.dev/typescript-0.95.10.wasm", "https://plugins.dprint.dev/json-0.20.0.wasm", "https://plugins.dprint.dev/markdown-0.19.0.wasm", "https://plugins.dprint.dev/toml-0.7.0.wasm", "https://plugins.dprint.dev/g-plane/pretty_yaml-v0.5.1.wasm", "https://plugins.dprint.dev/g-plane/malva-v0.14.2.wasm", "https://plugins.dprint.dev/dockerfile-0.3.3.wasm"]}