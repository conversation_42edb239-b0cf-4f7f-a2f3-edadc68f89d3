{"name": "@neonpro/database", "version": "1.0.0", "description": "NeonPro Healthcare Database Package - Consolidated Prisma ORM + Supabase integration with LGPD/ANVISA compliance", "main": "dist/src/index.js", "types": "dist/src/index.d.ts", "exports": {".": {"types": "./dist/src/index.d.ts", "default": "./dist/src/index.js"}, "./client": {"types": "./dist/src/index.d.ts", "default": "./dist/src/index.js"}, "./prisma": {"types": "./node_modules/@prisma/client/index.d.ts", "default": "./node_modules/@prisma/client/index.js"}}, "scripts": {"build": "tsc && prisma generate", "dev": "tsc --watch", "clean": "if exist dist rmdir /s /q dist", "type-check": "tsc --noEmit && prisma validate", "prisma:generate": "prisma generate", "prisma:migrate": "prisma migrate dev", "prisma:migrate:deploy": "prisma migrate deploy", "prisma:migrate:reset": "prisma migrate reset", "prisma:studio": "prisma studio", "prisma:seed": "tsx prisma/seed.ts", "prisma:db:push": "prisma db push", "prisma:db:pull": "prisma db pull", "prisma:format": "prisma format", "prisma:validate": "prisma validate", "db:generate": "prisma generate", "db:push": "prisma db push", "db:migrate": "prisma migrate dev", "db:deploy": "prisma migrate deploy", "db:reset": "prisma migrate reset", "db:seed": "tsx prisma/seed.ts", "db:studio": "prisma studio", "db:format": "prisma format", "db:validate": "prisma validate", "supabase:start": "supabase start", "supabase:stop": "supabase stop", "supabase:status": "supabase status", "supabase:reset": "supabase db reset", "supabase:migration:new": "supabase migration new", "supabase:migration:up": "supabase migration up", "supabase:migration:squash": "supabase migration squash", "supabase:generate:types": "supabase gen types typescript --project-id ownkoxryswokcdanrdgj > src/supabase-generated.ts", "supabase:functions:deploy": "supabase functions deploy", "supabase:functions:serve": "supabase functions serve", "supabase:seed": "supabase db seed", "supabase:diff": "supabase db diff", "supabase:lint": "supabase db lint", "supabase:push": "supabase db push", "supabase:pull": "supabase db pull"}, "keywords": ["database", "prisma", "supabase", "healthcare", "lgpd", "anvisa", "cfm", "neonpro", "postgresql", "orm", "real-time", "auth", "compliance"], "author": "NeonPro Healthcare Team", "license": "PROPRIETARY", "private": true, "dependencies": {"@prisma/client": "^5.22.0", "@supabase/ssr": "^0.5.1", "@supabase/supabase-js": "^2.45.4", "zod": "^3.23.8"}, "devDependencies": {"@types/node": "^22.10.2", "@types/react": "^19.0.0", "next": "^15.1.0", "prisma": "^5.22.0", "react": "^19.0.0", "supabase": "^1.127.4", "tsx": "^4.6.0", "typescript": "^5.7.2"}, "peerDependencies": {"next": ">=14.0.0", "react": ">=18.0.0"}, "engines": {"node": ">=18.0.0"}, "prisma": {"schema": "prisma/schema.prisma", "seed": "tsx prisma/seed.ts"}, "repository": {"type": "git", "url": "git+https://github.com/neonpro/neonpro.git", "directory": "packages/database"}}