{"compilerOptions": {"target": "ES2021", "lib": ["dom", "dom.iterable", "ES2021"], "allowJs": true, "skipLibCheck": true, "strict": true, "forceConsistentCasingInFileNames": true, "esModuleInterop": true, "module": "ESNext", "moduleResolution": "bundler", "resolveJsonModule": true, "isolatedModules": true, "jsx": "preserve", "downlevelIteration": true, "composite": true, "declaration": true, "declarationMap": true, "outDir": "./dist", "baseUrl": ".", "paths": {"@/*": ["./src/*"], "@/types/*": ["./src/types/*"], "@/utils/*": ["./src/utils/*"]}}, "include": ["src/**/*"], "exclude": ["node_modules", "dist", "coverage"]}