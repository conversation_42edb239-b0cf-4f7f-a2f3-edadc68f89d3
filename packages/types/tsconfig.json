{"compilerOptions": {"target": "ES2020", "lib": ["ES2020"], "allowJs": false, "skipLibCheck": true, "strict": true, "forceConsistentCasingInFileNames": true, "noEmit": false, "esModuleInterop": true, "module": "ESNext", "moduleResolution": "bundler", "resolveJsonModule": true, "isolatedModules": true, "declaration": true, "outDir": "./dist", "declarationDir": "./dist", "composite": true}, "include": ["src/**/*"], "exclude": ["node_modules", "dist"]}