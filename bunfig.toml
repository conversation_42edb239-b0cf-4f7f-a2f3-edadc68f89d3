# Bun Configuration for NeonPro Healthcare Platform
# Optimized for performance, security, and healthcare compliance

[install]
# Package installation configuration
cache = true
exact = false
frozen = false
dryRun = false
production = false
optional = true
dev = true
peer = true
registry = "https://registry.npmjs.org/"

# Performance optimizations
concurrent = 10
timeout = 30000

# Healthcare platform specific settings
verify = true
audit = true

[install.scopes]
# Scoped package registries (if needed for private packages)

[run]
# Script execution configuration
shell = "system"
silent = false

# Environment variables for healthcare compliance
[run.env]
NODE_ENV = "development"
HEALTHCARE_COMPLIANCE = "true"
LGPD_ENABLED = "true"
ANVISA_VALIDATION = "true"

[test]
# Testing configuration optimized for healthcare platform
coverage = true
timeout = 30000
preload = ["./tests/setup.ts"]

[build]
# Build optimizations
minify = true
sourcemap = true
target = "node18"

# Healthcare platform specific build settings
[build.env]
NEXT_TELEMETRY_DISABLED = "1"
HEALTHCARE_BUILD = "true"

[dev]
# Development server configuration
hot = true
port = 3000

[lockfile]
# Lockfile configuration for security and reproducibility
save = true
print = "yarn"

# Security settings for healthcare compliance
[security]
audit = true
advisories = true

# Workspace configuration for monorepo
[workspace]
# Enable workspace support for NeonPro monorepo
enabled = true

# Performance tuning for large healthcare platform
[performance]
# Memory and CPU optimizations
maxMemory = "4GB"
workers = 4

# Cache configuration for optimal performance
[cache]
dir = ".bun-cache"
ttl = 86400  # 24 hours

# Logging configuration for healthcare audit trails
[log]
level = "info"
format = "json"
audit = true
