{"$schema": "https://openapi.vercel.sh/vercel.json", "version": 2, "regions": ["gru1"], "framework": "nextjs", "installCommand": "pnpm install --frozen-lockfile", "buildCommand": "bash scripts/vercel-build.sh", "outputDirectory": "apps/web/.next", "rootDirectory": ".", "ignoreCommand": "git diff --quiet HEAD^ HEAD", "env": {"NODE_ENV": "production", "VERCEL": "1", "NEXT_PUBLIC_API_URL": "/api"}, "headers": [{"source": "/(.*)", "headers": [{"key": "Strict-Transport-Security", "value": "max-age=31536000; includeSubDomains; preload"}, {"key": "Content-Security-Policy", "value": "default-src 'self'; script-src 'self' 'unsafe-eval' 'unsafe-inline' https://va.vercel-scripts.com https://*.supabase.co; style-src 'self' 'unsafe-inline'; img-src 'self' data: https: blob:; font-src 'self' data:; connect-src 'self' https://*.supabase.co wss://*.supabase.co https://api.stripe.com; frame-ancestors 'none'; base-uri 'self'; form-action 'self'"}, {"key": "X-Content-Type-Options", "value": "nosniff"}, {"key": "X-Frame-Options", "value": "DENY"}, {"key": "X-XSS-Protection", "value": "1; mode=block"}, {"key": "Referrer-Policy", "value": "strict-origin-when-cross-origin"}, {"key": "Permissions-Policy", "value": "geolocation=(), microphone=(), camera=(), payment=(), usb=(), magnetometer=(), accelerometer=(), gyroscope=()"}]}, {"source": "/api/(.*)", "headers": [{"key": "Access-Control-Allow-Origin", "value": "*"}, {"key": "Access-Control-Allow-Methods", "value": "GET, POST, PUT, DELETE, OPTIONS"}, {"key": "Access-Control-Allow-Headers", "value": "Content-Type, Authorization, x-client-info, apikey"}, {"key": "Cache-Control", "value": "no-store, max-age=0"}]}, {"source": "/static/(.*)", "headers": [{"key": "Cache-Control", "value": "public, max-age=31536000, immutable"}]}], "functions": {"apps/web/app/api/**/*.{js,ts}": {"maxDuration": 30, "memory": 256, "runtime": "nodejs20.x"}}, "crons": []}