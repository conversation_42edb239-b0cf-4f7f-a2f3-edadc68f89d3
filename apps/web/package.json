{"name": "@neonpro/web", "version": "1.0.0", "private": true, "scripts": {"build": "next build", "build:vercel": "pnpm install && next build", "build:force": "next build --experimental-build-mode=skip-validation", "dev": "next dev", "start": "next start", "lint": "oxlint src --fix", "format": "dprint fmt", "format:check": "dprint check", "type-check": "tsc --noEmit", "test": "playwright test", "test:ui": "playwright test --ui", "test:debug": "playwright test --debug", "test:headed": "playwright test --headed", "test:report": "playwright show-report", "vercel-build": "prisma generate && prisma migrate deploy && next build"}, "prisma": {"seed": "ts-node --compiler-options {\"module\":\"CommonJS\"} prisma/seed.ts"}, "dependencies": {"@21st-extension/react": "^0.5.14", "@21st-extension/toolbar-next": "^0.5.14", "@ai-sdk/anthropic": "^2.0.4", "@ai-sdk/openai": "^2.0.15", "@aws-sdk/client-ses": "^3.864.0", "@hookform/resolvers": "^3.10.0", "@neonpro/database": "workspace:*", "@neonpro/domain": "workspace:*", "@neonpro/shared": "workspace:*", "@neonpro/ui": "workspace:*", "@neonpro/utils": "workspace:*", "@prisma/client": "^5.7.1", "@radix-ui/react-avatar": "^1.0.4", "@radix-ui/react-checkbox": "^1.3.3", "@radix-ui/react-dialog": "^1.0.5", "@radix-ui/react-dropdown-menu": "^2.0.6", "@radix-ui/react-label": "^2.0.2", "@radix-ui/react-popover": "^1.0.7", "@radix-ui/react-scroll-area": "^1.2.10", "@radix-ui/react-slider": "^1.3.6", "@radix-ui/react-slot": "^1.0.2", "@radix-ui/react-switch": "^1.2.6", "@radix-ui/react-toast": "^1.1.5", "@react-pdf/renderer": "^4.3.0", "@simplewebauthn/server": "^13.1.2", "@stripe/stripe-js": "^7.8.0", "@supabase/ssr": "^0.6.1", "@supabase/supabase-js": "^2.38.5", "@tanstack/react-router": "^1.58.15", "@tanstack/router-devtools": "^1.58.15", "@tanstack/router-vite-plugin": "^1.58.11", "@tensorflow/tfjs": "^4.22.0", "@trigger.dev/sdk": "^3.3.17", "@types/node-cron": "^3.0.11", "@types/nodemailer": "^7.0.0", "@types/papaparse": "^5.3.16", "@types/pdfkit": "^0.17.2", "@types/ua-parser-js": "^0.7.39", "@types/web-push": "^3.6.4", "ai": "^5.0.15", "axios": "^1.11.0", "canvg": "^4.0.3", "class-variance-authority": "^0.7.1", "clsx": "^2.0.0", "csv-parse": "^6.1.0", "dompurify": "^3.2.6", "dotenv": "^17.2.1", "framer-motion": "^12.23.12", "html2canvas": "^1.4.1", "ioredis": "^5.7.0", "jspdf": "^3.0.1", "jspdf-autotable": "^5.0.2", "lucide-react": "^0.539.0", "next": "^15.5.0", "next-auth": "^4.24.11", "next-themes": "^0.4.6", "node-cron": "^4.2.1", "nodemailer": "^7.0.5", "papaparse": "^5.5.3", "pdfkit": "^0.17.1", "react": "^19.1.1", "react-day-picker": "^9.8.1", "react-dom": "^19.1.1", "react-hook-form": "^7.62.0", "resend": "^6.0.1", "sonner": "^2.0.7", "stripe": "^18.4.0", "tailwind-merge": "^2.2.0", "typescript": "^5.9.2", "ua-parser-js": "^2.0.4", "web-push": "^3.6.7", "xlsx": "^0.18.5", "zod": "^3.23.8"}, "devDependencies": {"@axe-core/playwright": "^4.10.0", "@playwright/test": "^1.49.0", "@types/node": "^22.17.1", "@types/react": "^19.0.0", "@types/react-dom": "^19.0.0", "autoprefixer": "^10.0.1", "postcss": "^8", "prisma": "^5.7.1", "tailwindcss": "^3.3.0", "tailwindcss-animate": "^1.0.7", "ts-node": "^10.9.1"}}